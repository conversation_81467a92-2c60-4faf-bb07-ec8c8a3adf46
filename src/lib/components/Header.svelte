<script lang="ts">
  import { page } from "$app/stores";
  import { onNavigate, goto } from "$app/navigation";

  import * as m from "$lib/paraglide/messages.js";
  import { languageTag } from "$lib/paraglide/runtime";

  import A11yWidget from "./A11yWidget.svelte";
  import LoginModal from "./LoginModal.svelte";
  import Logo from "$lib/assets/images/logo-animated.svg";
  //@ts-ignore
  import SolarAccessibilityLinear from "~icons/solar/accessibility-linear";
  //@ts-ignore
  import SolarAltArrowDownLinear from "~icons/solar/alt-arrow-down-linear";
  //@ts-ignore
  import SolarAltArrowUpLinear from "~icons/solar/alt-arrow-up-linear";
  //@ts-ignore
  import SolarAltArrowRightLinear from "~icons/solar/alt-arrow-right-linear";

  //export let path: any;

  import { PUBLIC_RESULTS_URL } from "$env/static/public";
    import { i18n } from "$lib/i18n";
    import { availableLanguageTags } from "$lib/paraglide/runtime";
  const PUBLIC_RESULTS = PUBLIC_RESULTS_URL;

  let isMenuOpen = false;
  let isFacilityMenuOpen = false;
  let isPatientMenuOpen = false;
  let showLoginModal = false;

  // Automatycznie otwórz odpowiedni akordeon zależnie od ścieżki
  $: {
    if (isOnFacilityPage) {
      isFacilityMenuOpen = true;
      isPatientMenuOpen = false;
    } else {
      isFacilityMenuOpen = false;
      isPatientMenuOpen = true;
    }
  }

  // Efektywne blokowanie przewijania całej strony przy otwartym menu
  $: {
    if (typeof document !== "undefined") {
      if (isMenuOpen) {
        // Zastosuj style blokujące przewijanie
        document.body.style.overflow = 'hidden';
        document.body.style.height = '100%';
      } else {
        // Przywróć normalne przewijanie
        document.body.style.overflow = '';
        document.body.style.height = '';
      }
    }
  }

  function handleMenuClick(event: any) {
    if (event.target.tagName === "A" || event.target.tagName === "BUTTON") {
      isMenuOpen = false;
      // Usunięcie klasy no-scroll po kliknięciu w link lub przycisk
      if (typeof document !== "undefined") {
        document.body.classList.remove("no-scroll");
      }
    }
  }

  function toggleMenu() {
    isMenuOpen = !isMenuOpen;
    const tham = document.querySelector(".tham");
    if (tham) {
      tham.classList.toggle("tham-active");
    }
  }

  function toggleFacilityMenu() {
    isFacilityMenuOpen = !isFacilityMenuOpen;
    if (isFacilityMenuOpen) {
      isPatientMenuOpen = false; // Zamknij drugi akordeon, gdy otwieramy ten
    }
  }

  function togglePatientMenu() {
    isPatientMenuOpen = !isPatientMenuOpen;
    if (isPatientMenuOpen) {
      isFacilityMenuOpen = false; // Zamknij drugi akordeon, gdy otwieramy ten
    }
  }

  function openLoginModal() {
    showLoginModal = true;
  }

  $: lang = languageTag();
  $: showA11yWidget = false;

  // Sprawdzenie czy jesteśmy na stronie dla placówki
  $: isOnFacilityPage = $page.url.pathname.includes("/dla-profesjonalistow") ||
                        $page.url.pathname.includes("/professionals") ||
                        $page.url.pathname.includes("/dlya-profesionaliv");

  onNavigate((navigation) => {
    // Usunięcie klasy no-scroll przy zmianie routy
    if (typeof document !== "undefined") {
      document.body.classList.remove("no-scroll");
      isMenuOpen = false;
    }

    const tham = document.querySelector(".tham");
    if (tham && !isMenuOpen) {
      tham.classList.toggle("tham-active");
    }

    if (!document.startViewTransition) return;

    return new Promise((resolve) => {
      document.startViewTransition(async () => {
        resolve();
        await navigation.complete;
      });
    });
  });

  // Dodanie nasłuchiwania na zdarzenie beforeunload, aby odblokować scrollowanie przy przeładowaniu strony
  if (typeof window !== "undefined") {
    window.addEventListener("beforeunload", () => {
      document.body.classList.remove("no-scroll");
    });
  }
</script>

{#if showA11yWidget}
  <A11yWidget bind:showA11yWidget />
{/if}

<section id="topbar">
  <nav>
    <ul id="target-group">
      <li
        aria-current={$page.url.pathname.length == 1 ||
        $page.url.pathname.endsWith("/pl") ||
        $page.url.pathname.endsWith("/en") ||
        $page.url.pathname.endsWith("/uk") ||
        $page.url.pathname.endsWith("/pl/") ||
        $page.url.pathname.endsWith("/en/") ||
        $page.url.pathname.endsWith("/uk/") ||
        $page.url.pathname.includes("poradnik-zdrowia")
          ? "page"
          : undefined}
      >
        <a href="/">{m.forPatient()}</a>
      </li>
      <li
        aria-current={$page.url.pathname.includes("/dla-profesjonalistow") ||
        $page.url.pathname.includes("/professionals") ||
        $page.url.pathname.includes("/dlya-medychnogo-zakladu")
          ? "page"
          : undefined}
      >
        <a href="/professionals">{m.forMf()}</a>
      </li>
    </ul>
    <ul id="accessibility-toggle">
      <li>
        <button
          id="accesibility-button"
          on:click={() => (showA11yWidget = !showA11yWidget)}
        >
          <SolarAccessibilityLinear
            class="text-xl font-bold inline mr-1 relative top-[-1px]"
          />
          {m.accesibility()}
        </button>
      </li>
    </ul>
    <ul id="language-selector">
      {#each availableLanguageTags as lang}
        <li>
          <a
            href={i18n.route($page.url.pathname)}
            hreflang={lang}
            aria-current={lang === languageTag() ? "page" : undefined}
          >
            {lang}
          </a>
        </li>
      {/each}
    </ul>
  </nav>
</section>

<section id="mainbar" class:fixed-mainbar={isMenuOpen}>
  <nav>
    <a href="/" id="logo">
      <img src={Logo} alt="Logo Zbadani.pl" width="167" height="50" />
    </a>

    <ul id="main-nav">
      {#if $page.url.pathname.length === 1 || $page.url.pathname.endsWith("/pl") || $page.url.pathname.endsWith("/en") || $page.url.pathname.endsWith("/uk/") || $page.url.pathname.endsWith("/pl/") || $page.url.pathname.endsWith("/en/") || $page.url.pathname.endsWith("/uk/")}
        <li>
          <a target="_blank" href="https://pacjent.zbadani.pl/">{m.login()}</a>
        </li>
        <li>
          <a target="_blank" href="https://pacjent.zbadani.pl/register"
            >{m.register()}</a
          >
        </li>
        <li><a target="_blank" href={PUBLIC_RESULTS}>{m.results()}</a></li>
      {:else if $page.url.pathname.includes("/wow/")}
        <li><a href="/professionals#products">{m.products()}</a></li>
        <li class="highlightborder">
          <button on:click={openLoginModal}>{m.login_pro()}</button>
        </li>
        <li><a href="#skontaktuj-sie-z-nami">{m.contact()}</a></li>
      {:else if $page.url.pathname.includes("/syndose/")}
        <li><a href="/professionals#products">{m.products()}</a></li>
        <li class="highlightborder">
          <button on:click={openLoginModal}>{m.login_pro()}</button>
        </li>
        <li><a href="#skontaktuj-sie-z-nami">{m.contact()}</a></li>
      {:else if $page.url.pathname.includes("/dla-profesjonalistow/") || $page.url.pathname.includes("/professionals/") || $page.url.pathname.includes("/dlya-medychnogo-zakladu/")}
        <li><a href="/professionals#products">{m.products()}</a></li>
        <li class="highlightborder">
          <button on:click={openLoginModal}>{m.login_pro()}</button>
        </li>
        <li><a href="/contact">{m.contact()}</a></li>
      {/if}
    </ul>

    <button id="hamburger" class="lg:hidden ml-auto block mr-4" type="button" on:click={toggleMenu} on:keyup={toggleMenu}>
      <div class="tham tham-e-squeeze tham-w-6">
        <div class="tham-box">
          <div class="tham-inner bg-blue-500" />
        </div>
      </div>
    </button>

    <div class="drawer-menu" on:click={handleMenuClick} on:keyup={handleMenuClick} tabindex="0" role="button" class:isMenuOpen>
      <ul id="main-nav-mobile" class="text-blue-100 text-lg p-8 text-left flex flex-col h-full">
        {#if isOnFacilityPage}
          <!-- Sekcja "Dla pacjenta" (rozwijana) na stronach placówki -->
          <li class="menu-section">
            <button class="mb-4 w-full flex justify-between items-center" on:click|stopPropagation={togglePatientMenu}>
              <span>{m.forPatient()}</span>
              <span class="ml-auto">
                {#if isPatientMenuOpen}
                  <SolarAltArrowDownLinear />
                {:else}
                  <SolarAltArrowUpLinear />
                {/if}
              </span>
            </button>
            {#if isPatientMenuOpen}
              <ul class="submenu mb-4">
                <li>
                  <a class="mb-2 flex items-center" href="/">
                    <span class="w-full">{m.back_to_homepage ? m.back_to_homepage() : "Strona główna"}</span>
                    <SolarAltArrowRightLinear class="ml-1 text-sm" />
                  </a>
                </li>
                <li>
                  <a class="mb-2 flex items-center" href="https://pacjent.zbadani.pl/" target="_blank">
                    <span class="w-full">{m.login()}</span>
                    <SolarAltArrowRightLinear class="ml-1 text-sm" />
                  </a>
                </li>
                <li>
                  <a class="mb-2 flex items-center" href="https://pacjent.zbadani.pl/register" target="_blank">
                    <span class="w-full">{m.register()}</span>
                    <SolarAltArrowRightLinear class="ml-1 text-sm" />
                  </a>
                </li>
                <li>
                  <a class="mb-2 flex items-center" href={PUBLIC_RESULTS} target="_blank">
                    <span class="w-full">{m.results()}</span>
                    <SolarAltArrowRightLinear class="ml-1 text-sm" />
                  </a>
                </li>
              </ul>
            {/if}
          </li>

          <!-- Sekcja "Dla placówki" na stronach placówki (jako akordeon) -->
          <li class="menu-section">
            <button class="mb-4 w-full flex justify-between items-center" on:click|stopPropagation={toggleFacilityMenu}>
              <span>{m.forMf()}</span>
              <span class="ml-auto">
                {#if isFacilityMenuOpen}
                  <SolarAltArrowDownLinear />
                {:else}
                  <SolarAltArrowUpLinear />
                {/if}
              </span>
            </button>
            {#if isFacilityMenuOpen}
              <ul class="submenu mb-4">
                <li>
                  <a class="mb-2 flex items-center" href="/professionals#products">
                    <span class="w-full">{m.products()}</span>
                    <SolarAltArrowRightLinear class="ml-1 text-sm" />
                  </a>
                </li>
                <li>
                  <button class="mb-2 flex items-center w-full text-left" on:click={openLoginModal}>
                    <span class="w-full">{m.login_pro()}</span>
                    <SolarAltArrowRightLinear class="ml-1 text-sm" />
                  </button>
                </li>
                <li>
                  <a class="mb-2 flex items-center" href="/contact">
                    <span>{m.contact()}</span>
                    <SolarAltArrowRightLinear class="ml-1 text-sm" />
                  </a>
                </li>
              </ul>
            {/if}
          </li>
        {:else}
          <!-- Sekcja "Dla pacjenta" (rozwijana) na innych stronach -->
          <li class="menu-section">
            <button class="mb-4 w-full flex justify-between items-center" on:click|stopPropagation={togglePatientMenu}>
              <span>{m.forPatient()}</span>
              <span class="ml-auto">
                {#if isPatientMenuOpen}
                  <SolarAltArrowDownLinear />
                {:else}
                  <SolarAltArrowUpLinear />
                {/if}
              </span>
            </button>
            {#if isPatientMenuOpen}
              <ul class="submenu mb-4">
                <li>
                  <a class="mb-2 flex items-center" href="https://pacjent.zbadani.pl/" target="_blank">
                    <span class="w-full">{m.login()}</span>
                    <SolarAltArrowRightLinear class="ml-1 text-sm" />
                  </a>
                </li>
                <li>
                  <a class="mb-2 flex items-center" href="https://pacjent.zbadani.pl/register" target="_blank">
                    <span class="w-full">{m.register()}</span>
                    <SolarAltArrowRightLinear class="ml-1 text-sm" />
                  </a>
                </li>
                <li>
                  <a class="mb-2 flex items-center" href={PUBLIC_RESULTS} target="_blank">
                    <span class="w-full">{m.results()}</span>
                    <SolarAltArrowRightLinear class="ml-1 text-sm" />
                  </a>
                </li>
              </ul>
            {/if}
          </li>

          <!-- Sekcja "Dla placówki" (rozwijana) na innych stronach -->
          <li class="menu-section">
            <button class="mb-4 w-full flex justify-between items-center" on:click|stopPropagation={toggleFacilityMenu}>
              <span>{m.forMf()}</span>
              <span class="ml-auto">
                {#if isFacilityMenuOpen}
                  <SolarAltArrowDownLinear />
                {:else}
                  <SolarAltArrowUpLinear />
                {/if}
              </span>
            </button>
            {#if isFacilityMenuOpen}
              <ul class="submenu mb-4">
                <li>
                  <a class="mb-2 flex items-center" href="/professionals#products">
                    <span class="w-full">{m.products()}</span>
                    <SolarAltArrowRightLinear class="ml-1 text-sm" />
                  </a>
                </li>
                <li>
                  <button class="mb-2 flex items-center w-full text-left" on:click={openLoginModal}>
                    <span class="w-full">{m.login_pro()}</span>
                    <SolarAltArrowRightLinear class="ml-1 text-sm" />
                  </button>
                </li>
                <li>
                  <a class="mb-2 flex items-center" href="/contact">
                    <span class="w-full">{m.contact()}</span>
                    <SolarAltArrowRightLinear class="ml-1 text-sm" />
                  </a>
                </li>
              </ul>
            {/if}
          </li>
        {/if}

        <!-- Wybór języka (wyrównany do dołu) -->
        <div class="flex justify-center gap-3 mt-8 text-blue-300">
          {#each availableLanguageTags as lang}
            <li>
              <a
                class="py-2 px-4 inline-block"
                href={i18n.route($page.url.pathname)}
                hreflang={lang}
                aria-current={lang === languageTag() ? "page" : undefined}
              >
              {#if lang === 'pl'}
                PL
                {:else if lang === 'en'}
                  EN
                {:else if lang === 'uk'}
                  UA
                {:else}
                  {lang}
              {/if}
              </a>
            </li>
          {/each}
        </div>
      </ul>
    </div>
  </nav>
</section>

<LoginModal bind:show={showLoginModal} />

<style lang="postcss">
  #main-nav-mobile li[aria-current="page"] a, #main-nav-mobile li[aria-current="page"] button {
    @apply text-white font-semibold;
  }
  .drawer-menu {
    @apply fixed top-24 right-0 z-50 bg-blue-900 text-blue-100;
    height: calc(100vh - 96px);
    width: 100%;
    transform: translateX(100%);
    transition: transform 0.3s ease-in-out;
    overflow: hidden;
  }

  .isMenuOpen.drawer-menu {
    transform: translateX(0); /* Slide in */
  }

  .submenu {
    @apply transition-all duration-300 ease-in-out;
  }

  .submenu li a span, .submenu li button span  {
    @apply w-full
  }

  .menu-section {
    @apply text-blue-300
  }

  .fixed-mainbar {
    position: fixed !important;
    width: 100%;
    background: #fff;
    z-index: 40;
    top: 0;
    left: 0;
  }

  .menu-section {
    padding: 20px 20px 0 20px;
    background: #fff;
    border-radius: 10px;
    margin-bottom: 20px;
  }

  section#topbar {
    @apply w-full h-12 hidden lg:flex items-center bg-blue-900 relative z-20;
  }
  section#topbar nav {
    @apply flex mx-auto w-full px-8;
  }
  @media screen and (max-width: 640px) {
    section#topbar nav {
      @apply px-4;
    }
  }
  section#topbar ul#target-group {
    @apply flex gap-6 text-blue-300;
  }
  section#topbar ul#accessibility-toggle {
    @apply flex ml-auto mr-0 gap-6 text-blue-300;
  }
  section#topbar ul#language-selector {
    @apply flex text-blue-300;
  }
  section#topbar ul#language-selector li {
    @apply ml-4;
  }
  section#topbar ul#language-selector li a {
    @apply transition-all uppercase;
  }
  section#topbar ul#language-selector li a:hover {
    @apply cursor-pointer opacity-50;
  }

  section#topbar li a {
    @apply block;
  }
  section#topbar li {
    @apply relative transition-all leading-[50px];
  }
  #language-selector li a[aria-current="page"]::before,
  section#topbar li[aria-current="page"]::before {
    @apply border-b-2 border-b-blue-300;
    content: "";
    height: 2px;
    width: 100%;
    position: absolute;
    bottom: 1px;
    view-transition-name: active-page;
  }
  section#topbar li:hover {
    @apply opacity-50;
  }
  section#mainbar {
    @apply py-6 relative z-30;
  }

  section#mainbar #logo {
    @apply lg:ml-0 ml-4;
  }
  section#mainbar nav {
    @apply flex mx-auto items-center w-full px-8;
  }
  @media screen and (max-width: 640px) {
    section#mainbar nav {
      @apply px-4;
    }
  }
  section#mainbar ul#main-nav {
    @apply ml-auto lg:flex hidden;
  }
  section#mainbar ul#main-nav li a, section#mainbar ul#main-nav li button {
    @apply border-2 border-transparent rounded-xl text-blue-300 leading-none m-0 transition-all;
  }
  section#mainbar ul#main-nav li a, section#mainbar ul#main-nav li button {
    @apply p-3 inline-block;
  }
  section#mainbar ul#main-nav li:last-child a {
    @apply bg-blue-500 text-white ml-4;
  }
  section#mainbar ul#main-nav li.highlightborder a,
  section#mainbar ul#main-nav li.highlightborder button {
    @apply border-blue-300 ml-4;
  }
  section#mainbar ul#main-nav li button {
    @apply border-2 border-transparent rounded-xl text-blue-300 leading-none m-0 transition-all p-3 inline-block bg-transparent cursor-pointer;
  }
  section#mainbar ul#main-nav li:hover {
    @apply opacity-50;
  }
</style>
