<script lang="ts">
	import * as m from "$lib/paraglide/messages.js";
	import clsx, { type ClassValue } from 'clsx'
	import { twMerge } from 'tailwind-merge';

	import { createAccordion, melt } from '@melt-ui/svelte';
	import { slide } from 'svelte/transition';

	// @ts-ignore
    import SolarAltArrowDownOutline from '~icons/solar/alt-arrow-down-outline'
	// @ts-ignore
    import SolarAltArrowUpOutline from '~icons/solar/alt-arrow-up-outline'

	function cn(...classes: ClassValue[]) {
		return twMerge(clsx(classes))
	}

	let selectedAccordionItem: any = 'item-1';

	const {
		elements: { content, item, trigger, root },
		helpers: { isSelected },
	} = createAccordion({
		defaultValue: 'item-1',
		onValueChange: ({ curr, next }) => {
			selectedAccordionItem = next;
			return next
		},
	});

    interface Item {
        id: string;
        title: string;
        description: string;
    }

	const items: Item[] = [
		{
			id: 'item-1',
			title: 'Czy system WDM wymaga dodatkowej infrastruktury IT?',
			description: 'System WDM jest zoptymalizowany pod kątem minimalnych wymagań sprzętowych. W większości przypadków instalacja odbywa się na wirtualnych maszynach, dostosowanych do obecnej infrastruktury placówki. Szczegółowe wymagania są omawiane podczas procesu wdrożenia i dostosowywane indywidualnie.'
		},
		{
			id: 'item-2',
			title: 'Jak długo trwa wdrożenie systemu WDM?',
			description: 'Czas wdrożenia zależy od złożoności infrastruktury placówki i zakresu integracji. Typowy proces trwa od 2 do 4 tygodni, obejmując instalację, konfigurację, integrację z istniejącymi systemami oraz szkolenia personelu. Staramy się minimalizować zakłócenia w codziennej pracy placówki.'
		},
		{
			id: 'item-3',
			title: 'Jak wygląda proces integracji z istniejącymi systemami PACS/RIS?',
			description: 'Integracja z systemami PACS/RIS odbywa się za pomocą dedykowanego konektora, który przetwarza badania na potrzeby współdzielenia i udostępniania. Proces integracji jest projektowany indywidualnie dla każdej placówki, z uwzględnieniem specyfiki używanych systemów i formatów danych. Nasz zespół techniczny współpracuje z administratorami IT placówki, aby zapewnić płynną integrację.'
		},
		{
			id: 'item-4',
			title: 'Jak zapewnione jest bezpieczeństwo danych pacjentów?',
			description: 'System WDM implementuje zaawansowane mechanizmy bezpieczeństwa, w tym szyfrowanie danych podczas przesyłania i przechowywania, dwuetapową weryfikację tożsamości, szczegółowe logi dostępu oraz automatyczne kopie zapasowe. Wszystkie rozwiązania są zgodne z wymogami RODO i innymi regulacjami dotyczącymi ochrony danych medycznych. Przeprowadzamy regularne audyty bezpieczeństwa i aktualizacje systemu.'
		},
		{
			id: 'item-5',
			title: 'Czy istnieje możliwość dostosowania funkcjonalności systemu do specyficznych potrzeb naszej placówki?',
			description: 'Tak, system WDM oferuje szeroki zakres możliwości konfiguracyjnych. Możemy dostosować interfejs użytkownika, przepływy pracy, integracje z innymi systemami oraz metody udostępniania dokumentacji. Podczas procesu wdrożenia analizujemy potrzeby placówki i proponujemy optymalne rozwiązania, które mogą ewoluować wraz ze zmieniającymi się wymaganiami.'
		},
	];

	let className = '';
	export { className as class };
</script>	
	
<div>
	<div>
		<div
		class={cn(
			className,
		)}
		{...$root}
		>
		{#each items as { id, title, description }, i}
			<div
			use:melt={$item(id)}
			class="mb-4"
			>
			<h2 class="flex">
				<button
				use:melt={$trigger(id)}
				class={cn(
					'flex flex-1 cursor-pointer',
					'border border-[#BECDF8] rounded-xl hover:border-blue-500 hover:text-blue-500',
					'px-6 pt-6 pb-6',
                    'text-blue-100 bg-white text-left',
                    $isSelected(id) && 'no-underline rounded-b-none border-b-0 hover:border-[#BECDF8]'
				)}
				>
				{title}
                {#if $isSelected(id)}<SolarAltArrowUpOutline class="ml-auto text-2xl" />{:else}<SolarAltArrowDownOutline class="text-2xl ml-auto opacity-25" />{/if}
				</button>
			</h2>
			{#if $isSelected(id)}
				<div
				class={cn(
					'text-[#666] bg-blue-900',
				)}
				use:melt={$content(id)}
				transition:slide
				>
					<div class="px-6 pb-6 text-left bg-white rounded-b-xl border border-[#BECDF8] border-t-0">
						{@html description}
					</div>
				</div>
			{/if}
			</div>
		{/each}
		</div>

	</div>
</div>
