<script lang="ts">
    import type { SvelteComponent } from 'svelte';
	import * as m from "$lib/paraglide/messages.js";
	import { languageTag } from "$lib/paraglide/runtime";
	import clsx, { type ClassValue } from 'clsx'
	import { twMerge } from 'tailwind-merge';

	import { createAccordion, melt } from '@melt-ui/svelte';
	import { slide } from 'svelte/transition';

	// @ts-ignore
    import SolarAltArrowDownOutline from '~icons/solar/alt-arrow-down-outline'
	// @ts-ignore
    import SolarAltArrowUpOutline from '~icons/solar/alt-arrow-up-outline'
	// @ts-ignore
    import SolarDocumentTextOutline from '~icons/solar/document-text-outline'
	// @ts-ignore
    import SolarLogin3Outline from '~icons/solar/login-3-outline'

import LogoWWO from '$lib/assets/images/logo_wow_new.svg';
import LogoSynDose from '$lib/assets/images/logo_syndose.svg';
import LogoWDM from '$lib/assets/images/logo_wdm_new.svg';
import LogoAI from '$lib/assets/images/logo_ai_new.svg';

	import MfImg1 from '$lib/assets/images/mf_img_1.jpg'
	import MfImg2 from '$lib/assets/images/mf_img_2.jpg'

	import DoctorImg1 from '$lib/assets/images/p-syndose.jpg'
	import DoctorImg2 from '$lib/assets/images/p-wow.jpg'
	import DoctorImg3 from '$lib/assets/images/p-wdm.jpg'
	import DoctorImg4 from '$lib/assets/images/p-ai.jpg'


	function cn(...classes: ClassValue[]) {
		return twMerge(clsx(classes))
	}

	let selectedAccordionItem: any = 'item-1';

	const {
		elements: { content, item, trigger, root },
		helpers: { isSelected },
	} = createAccordion({
		defaultValue: 'item-1',
		onValueChange: ({ curr, next }) => {
			selectedAccordionItem = next;
			return next
		},
	});

    interface Item {
        id: string;
        icon: string;
        specialTitle: string;
		title: string;
        description: string;
        url: string;
    }

	const items: Item[] = [
		{
			id: 'item-1',
            icon: LogoSynDose,
			specialTitle: '',
			title: m.syndose_full(),
			description: m.mf_item1_description(),
			url: '/professionals/products/syndose'
		},
		{
			id: 'item-2',
            icon: LogoWWO,
			specialTitle: 'WOW',
			title: m.wow_full(),
			description: m.mf_item2_description(),
			url: '/professionals/products/wow'
		},
		{
			id: 'item-3',
            icon: LogoWDM,
			specialTitle: 'WDM',
			title: m.wdm_full(),
			description: m.mf_item3_description(),
			url: '/professionals/products/wdm'
		},
		{
			id: 'item-4',
            icon: LogoAI,
			specialTitle: 'AI',
			title: m.ai_for_diagnostics(),
			description: m.ai_pilot_program() + '. ' + m.ai_contact_us() + '.',
			url: '/contact'
		}
	];

	let className = '';
	export { className as class };
</script>

<div class="lg:grid grid-cols-2 gap-20">
	<div>
		<h2 class="text-3xl text-blue-100 font-medium mb-6">{m.products()}</h2>
		<div
		class={cn(
			'mt-8 mb-16',
			className,
		)}
		{...$root}
		>
		{#each items as { id, specialTitle, title, description, icon, url }, i}
			<div
			use:melt={$item(id)}
			class="mb-4"
			>
			<h2 class="flex">
				<button
				use:melt={$trigger(id)}
				class={cn(
					'flex flex-1 cursor-pointer items-center',
					'border-2 border-blue-900 rounded-xl hover:border-blue-500 hover:text-blue-500',
					'px-6 pt-6 pb-6',
                    'text-blue-100 font-semibold',
                    $isSelected(id) && 'hover:border-transparent no-underline bg-blue-900 rounded-t-xl rounded-b-none'
				)}
				>
                <img src={icon} class="mr-4 max-h-16" alt="Ikona" />
				<div class="text-left">
					{#if specialTitle}
						{#if $isSelected(id)}
						<p class="text-blue-500 bg-white rounded-lg px-2 py-1 text-sm inline-block text-left w-auto no-underline mb-4">{specialTitle}</p>
						{:else}
						<p class="text-blue-500 bg-blue-900 rounded-lg px-2 py-1 text-sm inline-block text-left w-auto no-underline mb-4">{specialTitle}</p>
						{/if}
					{/if}
					<p>{title}</p>
				</div>
                {#if $isSelected(id)}<SolarAltArrowUpOutline class="ml-auto text-2xl" />{:else}<SolarAltArrowDownOutline class="text-2xl ml-auto opacity-25" />{/if}
				</button>
			</h2>
			{#if $isSelected(id)}
				<div
				class={cn(
					'text-blue-100 bg-blue-900 rounded-b-xl',
				)}
				use:melt={$content(id)}
				transition:slide
				>
					<div class="px-6 pb-6">
						{description}
						<br />
						{#if url}<a class="btn inline-block mt-4" href={url}>{m.know_more()}</a>{/if}
					</div>
				</div>
			{/if}
			</div>
		{/each}
		</div>

	</div>
	<div class="flex items-center">
		{#if selectedAccordionItem === 'item-1' || selectedAccordionItem == undefined}
			<img width="680" height="500" class="rounded-xl" src={DoctorImg1} alt="SynDose grafika" />
		{/if}
		{#if selectedAccordionItem === 'item-2'}
			<img width="680" height="500" class="rounded-xl" src={DoctorImg2} alt="WWO grafika" />
		{/if}
		{#if selectedAccordionItem === 'item-3'}
			<img width="680" height="500" class="rounded-xl" src={DoctorImg3} alt="WDM grafika" />
		{/if}
		{#if selectedAccordionItem === 'item-4'}
			<img width="680" height="500" class="rounded-xl" src={DoctorImg4} alt="AI grafika" />
		{/if}
	</div>
</div>
